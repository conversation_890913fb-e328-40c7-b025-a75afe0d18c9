<?php $__env->startSection('title', $service->name . ' - خدماتنا - موتور لاين'); ?>

<?php $__env->startSection('meta_description', Str::limit($service->description ?? 'تفاصيل خدمة ' . $service->name, 160)); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="<?php echo e(route('site.home')); ?>" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>
                    الرئيسية
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="<?php echo e(route('site.services.index')); ?>" class="text-decoration-none">
                    خدماتنا
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <?php echo e($service->name); ?>

            </li>
        </ol>
    </nav>

    <div class="row">
        
        <div class="col-lg-8 mb-4">
            <div class="card brand-card shadow">
                
                <?php if($service->main_image_url): ?>
                    <img src="<?php echo e($service->main_image_url); ?>" 
                         class="card-img-top" 
                         alt="<?php echo e($service->name); ?>"
                         style="height: 300px; object-fit: cover;">
                <?php else: ?>
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                         style="height: 300px;">
                        <i class="fas fa-tools fa-4x text-muted"></i>
                    </div>
                <?php endif; ?>

                <div class="card-body">
                    
                    <?php if($service->category): ?>
                        <div class="mb-3">
                            <span class="badge brand-badge-secondary fs-6">
                                <i class="fas fa-tag me-1"></i>
                                <?php echo e($service->category->name); ?>

                            </span>
                        </div>
                    <?php endif; ?>

                    
                    <h1 class="card-title brand-text-primary mb-3">
                        <?php echo e($service->name); ?>

                    </h1>

                    
                    <div class="mb-4">
                        <span class="h3 brand-text-primary fw-bold">
                            <?php echo e($service->formatted_price); ?>

                        </span>
                    </div>

                    
                    <?php if($service->description): ?>
                        <div class="mb-4">
                            <h5 class="brand-text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                تفاصيل الخدمة
                            </h5>
                            <div class="text-muted lh-lg">
                                <?php echo nl2br(e($service->description)); ?>

                            </div>
                        </div>
                    <?php endif; ?>

                    
                    <div class="d-grid gap-2 d-md-flex">
                        <button type="button" 
                                class="btn brand-btn-primary btn-lg flex-md-fill"
                                data-service-id="<?php echo e($service->id); ?>"
                                data-service-name="<?php echo e($service->name); ?>"
                                data-bs-toggle="modal" 
                                data-bs-target="#serviceRequestModal">
                            <i class="fas fa-paper-plane me-2"></i>
                            اطلب الخدمة الآن
                        </button>
                        <a href="<?php echo e(route('site.services.index')); ?>" 
                           class="btn brand-btn-outline-primary btn-lg flex-md-fill">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للخدمات
                        </a>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="col-lg-4">
            
            <div class="card brand-card shadow mb-4">
                <div class="card-header brand-bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-tag text-muted me-2"></i>
                            <strong>الفئة:</strong> 
                            <?php echo e($service->category->name ?? 'غير محدد'); ?>

                        </li>
                        <li class="mb-2">
                            <i class="fas fa-money-bill-wave text-muted me-2"></i>
                            <strong>السعر:</strong> 
                            <span class="brand-text-primary fw-bold"><?php echo e($service->formatted_price); ?></span>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-clock text-muted me-2"></i>
                            <strong>وقت الاستجابة:</strong> 
                            خلال 24 ساعة
                        </li>
                        <li>
                            <i class="fas fa-shield-alt text-muted me-2"></i>
                            <strong>الضمان:</strong> 
                            حسب نوع الخدمة
                        </li>
                    </ul>
                </div>
            </div>

            
            <?php if($relatedServices->count() > 0): ?>
                <div class="card brand-card shadow">
                    <div class="card-header brand-bg-secondary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            خدمات مشابهة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php $__currentLoopData = $relatedServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="d-flex align-items-center mb-3 <?php echo e(!$loop->last ? 'border-bottom pb-3' : ''); ?>">
                                <?php if($relatedService->main_image_url): ?>
                                    <img src="<?php echo e($relatedService->main_image_url); ?>" 
                                         class="rounded me-3" 
                                         alt="<?php echo e($relatedService->name); ?>"
                                         style="width: 60px; height: 60px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-tools text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="<?php echo e(route('site.services.show', $relatedService->id)); ?>" 
                                           class="text-decoration-none brand-text-primary">
                                            <?php echo e($relatedService->name); ?>

                                        </a>
                                    </h6>
                                    <small class="text-muted"><?php echo e($relatedService->formatted_price); ?></small>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>


<div class="modal fade" id="serviceRequestModal" tabindex="-1" aria-labelledby="serviceRequestModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header brand-bg-primary text-white">
                <h5 class="modal-title" id="serviceRequestModalLabel">
                    <i class="fas fa-paper-plane me-2"></i>
                    طلب خدمة: <span id="modal-service-name"><?php echo e($service->name); ?></span>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="serviceRequestForm" action="<?php echo e(route('site.services.request')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="service_id" value="<?php echo e($service->id); ?>">
                    
                    <div class="alert brand-alert brand-alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم التواصل معك خلال 24 ساعة لتأكيد طلب الخدمة وتحديد موعد التنفيذ
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customer_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="customer_phone" class="form-label">رقم الجوال <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="customer_phone" name="customer_phone" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="customer_email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="customer_email" name="customer_email">
                    </div>

                    <div class="mb-3">
                        <label for="preferred_date" class="form-label">التاريخ المفضل للخدمة</label>
                        <input type="date" class="form-control" id="preferred_date" name="preferred_date" 
                               min="<?php echo e(date('Y-m-d', strtotime('+1 day'))); ?>">
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي تفاصيل إضافية تود إضافتها حول الخدمة المطلوبة..."></textarea>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn brand-btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>
                            إرسال طلب الخدمة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // معالجة إرسال نموذج طلب الخدمة
    const serviceRequestForm = document.getElementById('serviceRequestForm');
    if (serviceRequestForm) {
        serviceRequestForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // هنا يمكن إضافة منطق AJAX لإرسال الطلب
            // أو السماح للنموذج بالإرسال العادي
            alert('سيتم تنفيذ إرسال طلب الخدمة في المرحلة التالية');
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.breadcrumb-item + .breadcrumb-item::before {
    content: "‹";
    font-weight: bold;
}

.card-img-top {
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.02);
}

.lh-lg {
    line-height: 1.8 !important;
}

@media (max-width: 768px) {
    .card-img-top {
        height: 200px !important;
    }
    
    .btn-lg {
        font-size: 1rem;
        padding: 0.75rem 1rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('site.layouts.site_layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Project\MotorLine_10\Modules/ServiceManagement\Resources/views/site/services/show.blade.php ENDPATH**/ ?>