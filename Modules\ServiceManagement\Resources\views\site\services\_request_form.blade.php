{{--
    نموذج طلب الخدمة - Service Request Form
    
    المهمة: PH03-TASK-036 - FE-BLADE-SITE-SERVICE-REQUEST-FORM-VIEW-001
    الهدف: إنشاء ملف Blade view لنموذج طلب الخدمة في الموقع العام
    
    المدخلات المطلوبة:
    - $service (اختياري): كائن الخدمة إذا كان النموذج مخصص لخدمة معينة
    - $modalId (اختياري): معرف المودال، افتراضي 'serviceRequestModal'
    - $formId (اختياري): معرف النموذج، افتراضي 'serviceRequestForm'
    
    الاستخدام:
    @include('servicemanagement::site.services._request_form', ['service' => $service])
--}}

@php
    $modalId = $modalId ?? 'serviceRequestModal';
    $formId = $formId ?? 'serviceRequestForm';
    $serviceName = isset($service) ? $service->name : '';
    $serviceId = isset($service) ? $service->id : '';
    $servicePrice = isset($service) ? $service->price : '';
    $serviceCurrency = isset($service) ? $service->currency : 'ريال';
@endphp

{{-- مودال طلب الخدمة --}}
<div class="modal fade" id="{{ $modalId }}" tabindex="-1" aria-labelledby="{{ $modalId }}Label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header brand-bg-primary text-white">
                <h5 class="modal-title" id="{{ $modalId }}Label">
                    <i class="fas fa-paper-plane me-2"></i>
                    طلب خدمة
                    @if($serviceName)
                        : <span id="modal-service-name">{{ $serviceName }}</span>
                    @endif
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                {{-- عرض سعر الخدمة إذا كان متوفراً --}}
                @if($servicePrice)
                <div class="alert alert-info mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-tag me-2"></i>
                        <div>
                            <strong>سعر الخدمة:</strong>
                            <span class="fs-5 fw-bold text-primary">{{ number_format($servicePrice, 2) }} {{ $serviceCurrency }}</span>
                            <small class="text-muted d-block">شامل ضريبة القيمة المضافة</small>
                        </div>
                    </div>
                </div>
                @endif

                <form id="{{ $formId }}" action="{{ route('site.services.request') }}" method="POST">
                    @csrf
                    
                    {{-- معرف الخدمة كحقل مخفي --}}
                    <input type="hidden" id="service_id" name="service_id" value="{{ $serviceId }}">
                    
                    {{-- اسم الخدمة للعرض --}}
                    <div class="mb-3">
                        <label for="service_name_display" class="form-label">الخدمة المطلوبة</label>
                        <input type="text" class="form-control" id="service_name_display" 
                               value="{{ $serviceName }}" readonly>
                    </div>

                    {{-- الاسم الكامل --}}
                    <div class="mb-3">
                        <label for="full_name" class="form-label">
                            الاسم الكامل <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="full_name" name="full_name" 
                               placeholder="أدخل اسمك الكامل" required>
                        <div class="invalid-feedback">
                            يرجى إدخال الاسم الكامل
                        </div>
                    </div>

                    {{-- رقم الجوال --}}
                    <div class="mb-3">
                        <label for="mobile_number" class="form-label">
                            رقم الجوال <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <img src="https://flagcdn.com/w20/sa.png" alt="السعودية" class="me-1">
                                +966
                            </span>
                            <input type="tel" class="form-control" id="mobile_number" name="mobile_number" 
                                   placeholder="5xxxxxxxx" pattern="^5[0-9]{8}$" required>
                        </div>
                        <div class="form-text">
                            أدخل رقم الجوال بدون رمز الدولة (مثال: 501234567)
                        </div>
                        <div class="invalid-feedback">
                            يرجى إدخال رقم جوال صحيح يبدأ بـ 5 ويتكون من 9 أرقام
                        </div>
                    </div>

                    {{-- البريد الإلكتروني (اختياري) --}}
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            البريد الإلكتروني <span class="text-muted">(اختياري)</span>
                        </label>
                        <input type="email" class="form-control" id="email" name="email" 
                               placeholder="<EMAIL>">
                        <div class="invalid-feedback">
                            يرجى إدخال بريد إلكتروني صحيح
                        </div>
                    </div>

                    {{-- ملاحظات إضافية --}}
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي تفاصيل إضافية تود إضافتها..."></textarea>
                        <div class="form-text">
                            يمكنك إضافة أي تفاصيل أو متطلبات خاصة للخدمة
                        </div>
                    </div>

                    {{-- زر الإرسال --}}
                    <div class="d-grid">
                        <button type="submit" class="btn brand-btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>
                            أرسل طلبك الآن
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{{-- JavaScript للتعامل مع النموذج --}}
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // معالجة أزرار طلب الخدمة
    const requestButtons = document.querySelectorAll('.request-service-btn');
    const serviceIdInput = document.getElementById('service_id');
    const serviceNameDisplay = document.getElementById('service_name_display');
    const modalServiceName = document.getElementById('modal-service-name');

    requestButtons.forEach(button => {
        button.addEventListener('click', function() {
            const serviceId = this.dataset.serviceId;
            const serviceName = this.dataset.serviceName;
            
            if (serviceIdInput) serviceIdInput.value = serviceId;
            if (serviceNameDisplay) serviceNameDisplay.value = serviceName;
            if (modalServiceName) modalServiceName.textContent = serviceName;
        });
    });

    // معالجة إرسال نموذج طلب الخدمة
    const serviceRequestForm = document.getElementById('{{ $formId }}');
    if (serviceRequestForm) {
        serviceRequestForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // التحقق من صحة النموذج
            if (!this.checkValidity()) {
                e.stopPropagation();
                this.classList.add('was-validated');
                return;
            }

            // التحقق من رقم الجوال السعودي
            const mobileInput = document.getElementById('mobile_number');
            const mobilePattern = /^5[0-9]{8}$/;
            
            if (mobileInput && !mobilePattern.test(mobileInput.value)) {
                mobileInput.setCustomValidity('يرجى إدخال رقم جوال صحيح يبدأ بـ 5 ويتكون من 9 أرقام');
                mobileInput.classList.add('is-invalid');
                return;
            } else if (mobileInput) {
                mobileInput.setCustomValidity('');
                mobileInput.classList.remove('is-invalid');
            }

            // إرسال النموذج (سيتم تطوير منطق AJAX في المرحلة التالية)
            alert('سيتم تنفيذ إرسال طلب الخدمة في المرحلة التالية');
            
            // يمكن إلغاء التعليق على السطر التالي لإرسال النموذج فعلياً
            // this.submit();
        });
    }

    // إزالة رسائل الخطأ عند الكتابة
    const inputs = serviceRequestForm?.querySelectorAll('input, textarea');
    inputs?.forEach(input => {
        input.addEventListener('input', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });
});
</script>
@endpush

{{-- CSS إضافي للنموذج --}}
@push('styles')
<style>
.modal-header.brand-bg-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark, #0056b3) 100%);
}

.form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

.btn.brand-btn-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark, #0056b3) 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn.brand-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.3);
}

.input-group-text img {
    width: 20px;
    height: auto;
}

.alert-info {
    background-color: rgba(var(--bs-info-rgb), 0.1);
    border-color: rgba(var(--bs-info-rgb), 0.2);
}

.is-valid {
    border-color: var(--bs-success);
}

.is-invalid {
    border-color: var(--bs-danger);
}

@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .modal-lg {
        max-width: none;
    }
}
</style>
@endpush
