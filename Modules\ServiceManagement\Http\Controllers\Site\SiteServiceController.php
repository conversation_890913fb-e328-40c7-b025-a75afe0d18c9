<?php

namespace Modules\ServiceManagement\Http\Controllers\Site;

use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\ServiceManagement\Models\Service;
use Modules\ServiceManagement\Models\ServiceCategory;

/**
 * Site Service Controller
 *
 * يدير عرض الخدمات في الموقع العام
 * بناءً على PH03-TASK-034 و MOD-SERVICE-MGMT-FEAT-001
 *
 * @package Modules\ServiceManagement\Http\Controllers\Site
 * <AUTHOR> System
 * @version 1.0.0
 */
class SiteServiceController extends Controller
{
    /**
     * عرض قائمة الخدمات المتاحة في الموقع العام
     *
     * يجلب الخدمات النشطة مع فئاتها ويعرضها في صفحة الخدمات
     * بناءً على PH03-TASK-034 و MOD-SERVICE-MGMT-FEAT-001
     *
     * @return Renderable
     */
    public function index()
    {
        try {
            // جلب الخدمات النشطة مع فئاتها
            $services = Service::active()
                              ->withCategory()
                              ->with('media')
                              ->orderBy('created_at', 'desc')
                              ->get();

            // جلب فئات الخدمات النشطة للفلترة (اختياري)
            $categories = ServiceCategory::active()
                                        ->ordered()
                                        ->withCount(['services' => function ($query) {
                                            $query->where('status', true);
                                        }])
                                        ->having('services_count', '>', 0)
                                        ->get();

            return view('servicemanagement::site.services.index', compact('services', 'categories'));

        } catch (\Exception $e) {
            // في حالة حدوث خطأ، عرض صفحة فارغة مع رسالة
            return view('servicemanagement::site.services.index', [
                'services' => collect(),
                'categories' => collect(),
                'error' => 'حدث خطأ في تحميل الخدمات. يرجى المحاولة مرة أخرى.'
            ]);
        }
    }

    /**
     * عرض تفاصيل خدمة محددة
     *
     * @param int $id معرف الخدمة
     * @return Renderable
     */
    public function show($id)
    {
        try {
            $service = Service::active()
                             ->withCategory()
                             ->with('media')
                             ->findOrFail($id);

            // جلب خدمات مشابهة من نفس الفئة
            $relatedServices = Service::active()
                                     ->where('id', '!=', $id)
                                     ->where('category_id', $service->category_id)
                                     ->with('media')
                                     ->limit(3)
                                     ->get();

            return view('servicemanagement::site.services.show', compact('service', 'relatedServices'));

        } catch (\Exception $e) {
            abort(404, 'الخدمة المطلوبة غير موجودة');
        }
    }
}
