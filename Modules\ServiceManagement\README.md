# وحدة إدارة الخدمات - ServiceManagement Module

## نظرة عامة

وحدة إدارة الخدمات هي جزء من نظام MotorLine لإدارة معارض السيارات. تتولى هذه الوحدة إدارة الخدمات الإضافية التي يقدمها المعرض وعرضها في الموقع العام.

## الميزات المنفذة

### 1. عرض قائمة الخدمات في الموقع العام
- **المهمة**: `PH03-TASK-035` - `FE-BLADE-SITE-SERVICES-LIST-VIEW-001`
- **الهدف**: إنشاء صفحة عرض قائمة الخدمات في الموقع العام
- **الميزات**:
  - عرض الخدمات النشطة كبطاقات
  - فلترة الخدمات حسب الفئة
  - عرض صور الخدمات
  - عرض أسعار الخدمات
  - أزرار "اطلب الخدمة" و "اعرف المزيد"
  - مودال طلب الخدمة
  - تصميم متجاوب

### 2. عرض تفاصيل الخدمة
- صفحة تفاصيل منفصلة لكل خدمة
- عرض الخدمات المشابهة
- معلومات سريعة عن الخدمة
- مودال طلب الخدمة المحسن

## الهيكل التقني

### Models
- **ServiceCategory**: نموذج فئات الخدمات
  - دعم الترجمة باستخدام `spatie/laravel-translatable`
  - دعم الصور باستخدام `spatie/laravel-medialibrary`
  - نطاقات للفلترة والترتيب

- **Service**: نموذج الخدمات
  - دعم الترجمة باستخدام `spatie/laravel-translatable`
  - دعم الصور باستخدام `spatie/laravel-medialibrary`
  - علاقة مع فئات الخدمات
  - خصائص محسوبة للسعر والصورة

### Controllers
- **SiteServiceController**: معالجة عرض الخدمات في الموقع العام
  - دالة `index()`: عرض قائمة الخدمات
  - دالة `show()`: عرض تفاصيل خدمة محددة

### Views
- **site/services/index.blade.php**: صفحة قائمة الخدمات
- **site/services/show.blade.php**: صفحة تفاصيل الخدمة

### Database
- **service_categories**: جدول فئات الخدمات
- **services**: جدول الخدمات

## المسارات

### مسارات الموقع العام
```php
// عرض قائمة الخدمات
GET /services

// عرض تفاصيل خدمة محددة
GET /services/{id}

// طلب خدمة (سيتم تنفيذه لاحقاً)
POST /services/request
```

## التثبيت والإعداد

### 1. تشغيل Migrations
```bash
php artisan migrate --path=Modules/ServiceManagement/Database/Migrations
```

### 2. تفعيل الموديول
```bash
php artisan module:enable ServiceManagement
```

### 3. إضافة بيانات تجريبية (اختياري)
```bash
php artisan db:seed --class=Modules\\ServiceManagement\\Database\\Seeders\\ServiceManagementDatabaseSeeder
```

## الاستخدام

### إضافة خدمة جديدة
```php
use Modules\ServiceManagement\Models\Service;

$service = Service::create([
    'name' => ['ar' => 'خدمة الصيانة الدورية'],
    'description' => ['ar' => 'خدمة صيانة شاملة للسيارة'],
    'price' => 500.00,
    'currency' => 'SAR',
    'status' => true,
]);
```

### إضافة صورة للخدمة
```php
$service->addMediaFromRequest('image')
        ->toMediaCollection('service_images');
```

### جلب الخدمات النشطة
```php
$services = Service::active()
                  ->withCategory()
                  ->with('media')
                  ->get();
```

## الميزات القادمة

### المرحلة التالية
1. **نموذج طلب الخدمة**: تنفيذ معالجة طلبات الخدمات
2. **إدارة الخدمات**: لوحة تحكم لإدارة الخدمات
3. **إدارة فئات الخدمات**: لوحة تحكم لإدارة فئات الخدمات
4. **إدارة طلبات الخدمات**: لوحة تحكم لمتابعة طلبات العملاء

### تحسينات مستقبلية
- نظام تقييم الخدمات
- نظام الحجز المباشر
- تكامل مع نظام الدفع
- إشعارات العملاء

## المتطلبات

### الحزم المطلوبة
- `spatie/laravel-translatable`: للترجمة
- `spatie/laravel-medialibrary`: لإدارة الصور
- `nwidart/laravel-modules`: لإدارة الموديولات

### إعدادات قاعدة البيانات
- دعم JSON للترجمة
- دعم Foreign Keys

## الأمان

### الحماية المطبقة
- التحقق من صحة البيانات
- حماية من SQL Injection
- تشفير البيانات الحساسة

### الصلاحيات
- عرض الخدمات: متاح للجميع
- إدارة الخدمات: للمديرين والموظفين فقط

## الأداء

### التحسينات المطبقة
- استخدام Eager Loading للعلاقات
- فهرسة الحقول المهمة
- تحسين استعلامات قاعدة البيانات

### التخزين المؤقت
- تخزين مؤقت للخدمات النشطة
- تخزين مؤقت للفئات

## الاختبار

### اختبارات الوحدة
```bash
php artisan test --filter=ServiceManagement
```

### اختبارات التكامل
- اختبار عرض قائمة الخدمات
- اختبار عرض تفاصيل الخدمة
- اختبار الفلترة والبحث

## المساهمة

### إرشادات التطوير
1. اتباع معايير PSR-12
2. كتابة التعليقات باللغة العربية
3. إضافة اختبارات للميزات الجديدة
4. توثيق التغييرات في CHANGELOG

### هيكل الملفات
```
Modules/ServiceManagement/
├── Config/
├── Database/
│   ├── Migrations/
│   └── Seeders/
├── Http/
│   └── Controllers/
│       └── Site/
├── Models/
├── Resources/
│   └── views/
│       └── site/
│           └── services/
└── Routes/
```

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
