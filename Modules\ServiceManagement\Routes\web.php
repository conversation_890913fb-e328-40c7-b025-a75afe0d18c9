<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Modules\ServiceManagement\Http\Controllers\Site\SiteServiceController;

/*
|--------------------------------------------------------------------------
| Site Routes (Public)
|--------------------------------------------------------------------------
*/

// مسارات الخدمات في الموقع العام
Route::prefix('services')->name('site.services.')->group(function() {
    Route::get('/', [SiteServiceController::class, 'index'])->name('index');
    Route::get('/{id}', [SiteServiceController::class, 'show'])->name('show');

    // مسار طلب الخدمة (سيتم تنفيذه في مهمة لاحقة)
    Route::post('/request', function() {
        return response()->json(['message' => 'سيتم تنفيذ هذه الميزة قريباً']);
    })->name('request');
});

/*
|--------------------------------------------------------------------------
| Admin Routes (Dashboard)
|--------------------------------------------------------------------------
*/

// مسارات إدارة الخدمات في لوحة التحكم (سيتم تنفيذها لاحقاً)
Route::prefix('admin/services')->name('admin.services.')->middleware(['auth', 'role:Super Admin|Employee'])->group(function() {
    // سيتم إضافة مسارات إدارة الخدمات هنا لاحقاً
});
